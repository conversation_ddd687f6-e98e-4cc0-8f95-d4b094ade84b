#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <sstream>
#include <regex>
#ifdef _WIN32
#include <windows.h>
#include <shellapi.h>
#include <shlobj.h>
#include <objbase.h>
#include <shlguid.h>
#endif
#include "../utils/output.hpp"
#include "config.hpp"
#include "manifest.hpp"
#include "dependency_resolver.hpp"
#include "../utils/download_manager.hpp"
#include "../utils/extractor.hpp"
#include "../utils/shim_manager.hpp"

namespace sco {

struct InstallationInfo {
    std::string app_name;
    std::string version;
    std::string bucket;
    std::filesystem::path install_path;
    std::chrono::system_clock::time_point install_time;
    std::vector<std::string> installed_files;
    std::vector<std::string> created_shims;
};

class InstallManager {
public:
    struct InstallOptions {
        bool global = false;
        bool skip_dependencies = false;
        bool force_reinstall = false;
        bool no_cache = false;
        std::string architecture = "64bit";
    };
    
    struct InstallResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> installed_apps;
        std::vector<std::string> failed_apps;
        std::chrono::milliseconds total_duration{0};
    };
    
    static InstallResult install_apps(const std::vector<std::string>& app_names, 
                                    const InstallOptions& options = {}) {
        InstallManager manager(options);
        return manager.perform_installation(app_names);
    }
    
private:
    InstallOptions options_;
    Config& config_;
    
    explicit InstallManager(const InstallOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    InstallResult perform_installation(const std::vector<std::string>& app_names) {
        InstallResult result;
        auto start_time = std::chrono::steady_clock::now();

        output::info("Starting installation of {} app(s)", app_names.size());

        try {
            // Ensure 7zip is available for extraction
            if (!ensure_7zip_available()) {
                result.error_message = "Failed to ensure 7zip is available for extraction";
                return result;
            }
            // Step 1: Resolve dependencies
            std::vector<std::string> install_order;
            if (!options_.skip_dependencies) {
                auto resolve_result = DependencyResolver::resolve(app_names);
                if (!resolve_result.success) {
                    result.error_message = "Dependency resolution failed";
                    if (!resolve_result.circular_dependencies.empty()) {
                        result.error_message += ": Circular dependencies detected";
                    }
                    if (!resolve_result.missing_dependencies.empty()) {
                        result.error_message += ": Missing dependencies";
                    }
                    return result;
                }
                install_order = resolve_result.install_order;
            } else {
                install_order = app_names;
            }
            
            // Step 2: Filter out already installed apps (unless force reinstall)
            if (!options_.force_reinstall) {
                install_order = DependencyResolver::filter_installed_apps(install_order);
            }
            
            if (install_order.empty()) {
                output::info("All requested apps are already installed");
                result.success = true;
                return result;
            }

            output::info("Will install {} app(s) in order: {}", install_order.size(),
                       join_strings(install_order, ", "));
            
            // Step 3: Install each app in order
            for (const auto& app_name : install_order) {
                if (install_single_app(app_name)) {
                    result.installed_apps.push_back(app_name);
                    output::info("Successfully installed: {}", app_name);
                } else {
                    result.failed_apps.push_back(app_name);
                    output::error("Failed to install: {}", app_name);
                    
                    // Decide whether to continue or abort
                    if (std::find(app_names.begin(), app_names.end(), app_name) != app_names.end()) {
                        // This was a requested app, not a dependency - abort
                        result.error_message = "Failed to install requested app: " + app_name;
                        break;
                    }
                    // This was a dependency - continue but log the failure
                }
            }
            
            result.success = result.failed_apps.empty() || 
                           (!result.installed_apps.empty() && result.failed_apps.size() < install_order.size());
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            output::error("Installation failed with exception: {}", e.what());
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        output::info("Installation completed in {}ms. Success: {}, Installed: {}, Failed: {}",
                   result.total_duration.count(), result.success,
                   result.installed_apps.size(), result.failed_apps.size());
        
        return result;
    }
    
    bool install_single_app(const std::string& app_name) {
        try {
            // Step 1: Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                output::error("Couldn't find manifest for '{}'", app_name);
                return false;
            }

            // Show Scoop-style installation header
            std::cout << "Installing '" << manifest.name << "' (" << manifest.version << ") ["
                      << options_.architecture << "]";
            if (!manifest.bucket.empty()) {
                std::cout << " from '" << manifest.bucket << "' bucket";
            }
            std::cout << "\n";

            // Step 2: Prepare installation directory (version-specific)
            auto version_dir = prepare_version_directory(manifest);
            if (version_dir.empty()) {
                output::error("Failed to prepare app directory for: {}", app_name);
                return false;
            }

            // Keep reference to original directory before linking
            auto original_dir = version_dir;

            // Step 3: Download files
            auto urls = manifest.get_urls(options_.architecture);
            if (urls.empty()) {
                output::error("No download URLs found for: {}", app_name);
                return false;
            }

            std::vector<std::filesystem::path> downloaded_files;
            for (const auto& url_info : urls) {
                auto downloaded_file = download_file(url_info, version_dir, manifest.name, manifest.version);
                if (downloaded_file.empty()) {
                    output::error("Failed to download file for: {}", app_name);
                    return false;
                }
                downloaded_files.push_back(downloaded_file);
            }

            // Step 4: Extract files
            for (const auto& downloaded_file : downloaded_files) {
                if (!extract_file(downloaded_file, version_dir, manifest)) {
                    output::error("Failed to extract file for: {}", app_name);
                    return false;
                }
            }

            // Step 5: Run pre-install script
            auto pre_install_script = manifest.get_pre_install(options_.architecture);
            if (!pre_install_script.empty()) {
                if (!run_script(pre_install_script, version_dir, "pre-install")) {
                    output::warn("Pre-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }

            // Step 6: Run installer if present
            run_installer(manifest, version_dir, false); // false = install mode

            // Step 7: Ensure install directory is not in PATH
            ensure_install_dir_not_in_path(version_dir);

            // Step 8: Link current directory (Scoop's link_current)
            auto current_dir = link_current_directory(version_dir);

            // Step 9: Create shims (using current directory)
            auto bin_entries = manifest.get_bin(options_.architecture);
            if (!bin_entries.empty()) {
                if (!ShimManager::create_shims_for_app(app_name, current_dir, bin_entries)) {
                    output::warn("Failed to create some shims for: {}", app_name);
                    // Don't fail installation for shim creation failures
                }
            }

            // Step 10: Create start menu shortcuts
            create_startmenu_shortcuts(manifest, current_dir);

            // Step 11: Install PowerShell modules
            install_psmodule(manifest, current_dir);

            // Step 12: Add to PATH environment variable
            env_add_path(manifest, current_dir);

            // Step 13: Set environment variables
            env_set(manifest);

            // Step 14: Handle persist directories
            handle_persist_directories(manifest, original_dir);

            // Step 15: Set persist permissions
            persist_permission(manifest);

            // Step 16: Run post-install script
            auto post_install_script = manifest.get_post_install(options_.architecture);
            if (!post_install_script.empty()) {
                if (!run_script(post_install_script, version_dir, "post-install")) {
                    output::warn("Post-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }

            // Step 17: Save installation info (Scoop's save_installed_manifest and save_install_info)
            if (!save_installation_info(manifest, version_dir)) {
                output::warn("Failed to save installation info for: {}", app_name);
                // Don't fail installation for this
            }

            // Step 18: Show success message
            std::cout << "'" << manifest.name << "' (" << manifest.version << ") was installed successfully!\n";

            // Step 19: Show notes if any
            show_app_notes(manifest, current_dir, original_dir);
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Exception during installation of {}: {}", app_name, e.what());
            return false;
        }
    }
    
    std::filesystem::path prepare_version_directory(const Manifest& manifest) {
        auto apps_dir = options_.global ? config_.get_global_apps_dir() : config_.get_apps_dir();
        auto app_dir = apps_dir / manifest.name;
        auto version_dir = app_dir / manifest.version;

        try {
            // Create version-specific directory (Scoop's ensure versiondir)
            std::filesystem::create_directories(version_dir);

            return version_dir;

        } catch (const std::exception& e) {
            output::error("Failed to prepare version directory: {}", e.what());
            return {};
        }
    }

    std::filesystem::path link_current_directory(const std::filesystem::path& version_dir) {
        // Scoop's link_current function
        auto app_dir = version_dir.parent_path();
        auto current_dir = app_dir / "current";

        try {
            // Remove existing "current" symlink/directory
            if (std::filesystem::exists(current_dir)) {
                std::filesystem::remove_all(current_dir);
            }

            // Create "current" junction/symlink to version directory
#ifdef _WIN32
            // On Windows, create a junction point (like Scoop)
            std::string command = "mklink /J \"" + current_dir.string() + "\" \"" + version_dir.string() + "\"";
            int result = system(command.c_str());
            if (result != 0) {
                output::warn("Failed to create junction, using directory copy instead");
                std::filesystem::create_directories(current_dir);
                return version_dir; // Return version dir if junction fails
            }
#else
            // On Unix-like systems, create a symbolic link
            std::filesystem::create_symlink(version_dir, current_dir);
#endif

            return current_dir;

        } catch (const std::exception& e) {
            output::warn("Failed to create current directory link: {}", e.what());
            return version_dir; // Return version dir as fallback
        }
    }
    
    std::filesystem::path download_file(const ManifestUrl& url_info,
                                      const std::filesystem::path& app_dir,
                                      const std::string& app_name,
                                      const std::string& version) {
        auto cache_dir = config_.get_cache_dir();
        std::filesystem::create_directories(cache_dir);

        // Generate Scoop-compatible filename: app#version#hash.ext
        std::string filename = DownloadManager::generate_cache_filename(
            app_name, version, url_info.url, url_info.hash);

        auto cache_file = cache_dir / filename;
        
        // Check if file already exists in cache and hash matches
        if (std::filesystem::exists(cache_file) && !options_.no_cache) {
            if (DownloadManager::verify_hash(cache_file, url_info.hash)) {
                std::cout << "Loading " << filename << " from cache.\n";
                return cache_file;
            } else {
                output::warn("Cached file hash mismatch, re-downloading");
                std::filesystem::remove(cache_file);
            }
        }
        auto result = DownloadManager::download_with_progress(url_info.url, cache_file);

        if (!result.success) {
            output::error("Download failed: {}", result.error_message);
            return {};
        }

        // Verify hash
        if (!DownloadManager::verify_hash(cache_file, url_info.hash)) {
            output::error("Downloaded file hash verification failed");
            std::filesystem::remove(cache_file);
            return {};
        }

        std::cout << "Checking hash of " << filename << " ... ok.\n";
        
        return cache_file;
    }
    
    bool extract_file(const std::filesystem::path& archive_path,
                     const std::filesystem::path& app_dir,
                     const Manifest& manifest) {
        
        if (!Extractor::is_extractable(archive_path)) {
            // File is not an archive, just copy it
            try {
                auto dest_file = app_dir / archive_path.filename();
                std::filesystem::copy_file(archive_path, dest_file);
                return true;
            } catch (const std::exception& e) {
                output::error("Failed to copy file: {}", e.what());
                return false;
            }
        }
        
        auto extract_dir = manifest.get_architecture(options_.architecture) ? 
                          manifest.get_architecture(options_.architecture)->extract_dir : 
                          manifest.extract_dir;
        auto extract_to = manifest.get_architecture(options_.architecture) ? 
                         manifest.get_architecture(options_.architecture)->extract_to : 
                         manifest.extract_to;
        
        std::cout << "Extracting " << archive_path.filename().string() << " ... ";
        auto result = Extractor::extract_archive(archive_path, app_dir, extract_dir, extract_to);

        if (!result.success) {
            std::cout << "failed.\n";
            output::error("Extraction failed: {}", result.error_message);
            return false;
        }

        std::cout << "done.\n";
        
        return true;
    }
    
    bool run_script(const std::string& script, 
                   const std::filesystem::path& app_dir,
                   const std::string& script_type) {
        output::info("Running {} script", script_type);
        
        try {
            // Create temporary script file
            auto temp_script = app_dir / ("temp_" + script_type + ".ps1");
            
            std::ofstream script_file(temp_script);
            if (!script_file.is_open()) {
                output::error("Failed to create temporary script file");
                return false;
            }
            
            script_file << script;
            script_file.close();
            
            // Execute script
            std::string command = "powershell.exe -ExecutionPolicy Bypass -File \"" + 
                                temp_script.string() + "\"";
            
            output::debug("Executing script: {}", command);

            int exit_code = system(command.c_str());

            // Clean up temporary script
            std::filesystem::remove(temp_script);

            if (exit_code == 0) {
                output::info("{} script completed successfully", script_type);
                return true;
            } else {
                output::error("{} script failed with exit code: {}", script_type, exit_code);
                return false;
            }
            
        } catch (const std::exception& e) {
            output::error("Failed to run {} script: {}", script_type, e.what());
            return false;
        }
    }
    
    bool save_installation_info(const Manifest& manifest, 
                              const std::filesystem::path& app_dir) {
        try {
            InstallationInfo info;
            info.app_name = manifest.name;
            info.version = manifest.version;
            info.bucket = manifest.bucket;
            info.install_path = app_dir;
            info.install_time = std::chrono::system_clock::now();
            
            // Save to JSON file (use scoop-compatible filename)
            auto info_file = app_dir / "install.json";
            std::ofstream file(info_file);
            if (!file.is_open()) {
                return false;
            }
            
            // Create scoop-compatible install.json format
            nlohmann::json json;
            json["bucket"] = info.bucket;
            json["architecture"] = options_.architecture;
            // Optional: add url if available from manifest
            // json["url"] = manifest.url;
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Failed to save installation info: {}", e.what());
            return false;
        }
    }
    
    bool ensure_7zip_available() {
        // Check if 7zip is already available
        if (Extractor::is_7zip_available()) {
            output::debug("7zip is already available");
            return true;
        }

        // Check if 7zip is installed via Scoop but not in PATH
        if (DependencyResolver::is_app_installed("7zip")) {
            output::debug("7zip is installed via Scoop");
            return true;
        }

        output::info("7zip not found, installing it first...");

        // Install 7zip first (without dependencies to avoid recursion)
        InstallOptions seven_zip_options = options_;
        seven_zip_options.skip_dependencies = true;

        // Temporarily create a new manager to avoid recursion
        InstallManager seven_zip_manager(seven_zip_options);
        auto seven_zip_result = seven_zip_manager.install_single_app("7zip");

        if (!seven_zip_result) {
            output::error("Failed to install 7zip");
            return false;
        }

        output::info("7zip installed successfully");
        return true;
    }

    template<typename Container>
    std::string join_strings(const Container& container, const std::string& delimiter) {
        if (container.empty()) return "";

        std::ostringstream oss;
        auto it = container.begin();
        oss << *it;
        ++it;

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }

    void create_shortcuts(const Manifest& manifest, const std::filesystem::path& app_dir) {
        auto shortcuts = manifest.get_shortcuts();
        if (shortcuts.empty()) return;

        for (const auto& shortcut : shortcuts) {
            // Extract shortcut info - format: ["executable.exe", "Shortcut Name"]
            if (shortcut.size() >= 2) {
                std::string executable = shortcut[0];
                std::string shortcut_name = shortcut[1];

                std::cout << "Creating shortcut for " << shortcut_name << " (" << executable << ")\n";

                // TODO: Implement actual shortcut creation
                // For now, just log the action
            }
        }
    }

    void handle_persist_directories(const Manifest& manifest, const std::filesystem::path& app_dir) {
        auto persist_dirs = manifest.get_persist();
        if (persist_dirs.empty()) return;

        // Get persist directory for this app
        auto persist_base_dir = get_persist_directory(manifest.name);

        try {
            // Ensure persist base directory exists
            std::filesystem::create_directories(persist_base_dir);
        } catch (const std::exception& e) {
            output::error("Failed to create persist directory: {}", e.what());
            return;
        }

        for (const auto& persist_entry : persist_dirs) {
            try {
                std::string source_path, target_path;

                // Parse persist entry - can be string or array [source, target]
                if (persist_entry.find(',') != std::string::npos) {
                    // Handle array format: "source,target"
                    size_t comma_pos = persist_entry.find(',');
                    source_path = persist_entry.substr(0, comma_pos);
                    target_path = persist_entry.substr(comma_pos + 1);

                    // Trim whitespace
                    source_path.erase(0, source_path.find_first_not_of(" \t"));
                    source_path.erase(source_path.find_last_not_of(" \t") + 1);
                    target_path.erase(0, target_path.find_first_not_of(" \t"));
                    target_path.erase(target_path.find_last_not_of(" \t") + 1);
                } else {
                    // Simple string format
                    source_path = persist_entry;
                    target_path = persist_entry;
                }

                // Remove trailing slashes/backslashes
                while (!source_path.empty() && (source_path.back() == '/' || source_path.back() == '\\')) {
                    source_path.pop_back();
                }

                std::cout << "Persisting " << source_path << "\n";

                auto source_full_path = app_dir / source_path;
                auto target_full_path = persist_base_dir / target_path;

                // Create target directory structure if needed
                if (target_full_path.has_parent_path()) {
                    std::filesystem::create_directories(target_full_path.parent_path());
                }

                // If source exists in app directory, move it to persist directory
                if (std::filesystem::exists(source_full_path)) {
                    if (!std::filesystem::exists(target_full_path)) {
                        // Move existing data to persist directory
                        if (std::filesystem::is_directory(source_full_path)) {
                            std::filesystem::create_directories(target_full_path);
                            copy_directory_contents(source_full_path, target_full_path);
                            std::filesystem::remove_all(source_full_path);
                        } else {
                            std::filesystem::rename(source_full_path, target_full_path);
                        }
                    } else {
                        // Target already exists, remove source
                        std::filesystem::remove_all(source_full_path);
                    }
                }

                // Create junction/symlink from app directory to persist directory
                create_persist_link(source_full_path, target_full_path);

            } catch (const std::exception& e) {
                output::error("Failed to persist {}: {}", persist_entry, e.what());
            }
        }
    }

    void show_app_notes(const Manifest& manifest,
                       const std::filesystem::path& current_dir,
                       const std::filesystem::path& original_dir) {
        auto notes = manifest.get_notes();
        if (notes.empty()) return;

        std::cout << "\nNotes\n";
        std::cout << "-----\n";

        // Substitute variables like Scoop does
        std::string processed_notes = notes;

        // Replace $dir with current directory
        size_t pos = 0;
        while ((pos = processed_notes.find("$dir", pos)) != std::string::npos) {
            processed_notes.replace(pos, 4, current_dir.string());
            pos += current_dir.string().length();
        }

        // Replace $original_dir with original directory
        pos = 0;
        while ((pos = processed_notes.find("$original_dir", pos)) != std::string::npos) {
            processed_notes.replace(pos, 13, original_dir.string());
            pos += original_dir.string().length();
        }

        std::cout << processed_notes << "\n";
    }

    void run_installer(const Manifest& manifest, const std::filesystem::path& version_dir, bool uninstall) {
        // Scoop's Invoke-Installer function
        auto installer = uninstall ? manifest.get_uninstaller(options_.architecture) :
                                   manifest.get_installer(options_.architecture);

        if (installer.file.empty() && installer.args.empty()) {
            return; // No installer defined
        }

        std::string type = uninstall ? "uninstaller" : "installer";
        std::cout << "Running " << type << "...\n";

        try {
            // Determine installer file path
            std::filesystem::path installer_path;
            if (!installer.file.empty()) {
                installer_path = version_dir / installer.file;
            } else {
                // Use first downloaded file if no specific installer file is specified
                auto urls = manifest.get_urls(options_.architecture);
                if (!urls.empty()) {
                    std::string filename = extract_filename_from_url(urls[0].url);
                    installer_path = version_dir / filename;
                }
            }

            // Check if installer file exists and is within app directory
            if (!std::filesystem::exists(installer_path)) {
                output::error("{} {} is missing", type, installer_path.string());
                return;
            }

            if (!is_path_within_directory(version_dir, installer_path)) {
                output::error("Error in manifest: {} {} is outside the app directory", type, installer_path.string());
                return;
            }

            // Prepare arguments with variable substitution
            std::vector<std::string> final_args;
            for (const auto& arg : installer.args) {
                std::string substituted_arg = substitute_variables(arg, version_dir, manifest.version);
                final_args.push_back(substituted_arg);
            }

            // Execute installer
            bool success = false;
            if (installer_path.extension() == ".ps1") {
                success = execute_powershell_script(installer_path, final_args);
            } else {
                success = execute_installer(installer_path, final_args, type);
            }

            if (!success) {
                if (uninstall) {
                    output::error("Uninstallation aborted");
                } else {
                    output::error("Installation aborted. You might need to run 'sco uninstall {}' before trying again", manifest.name);
                }
                return;
            }

            // Remove installer file unless "keep" flag is set
            if (!installer.keep && std::filesystem::exists(installer_path)) {
                try {
                    std::filesystem::remove(installer_path);
                } catch (const std::exception& e) {
                    output::warn("Failed to remove installer file: {}", e.what());
                }
            }

        } catch (const std::exception& e) {
            output::error("Failed to run {}: {}", type, e.what());
        }
    }

    void ensure_install_dir_not_in_path(const std::filesystem::path& install_dir) {
        // Scoop's ensure_install_dir_not_in_path function
        try {
            std::string current_path = get_env_var("PATH");
            if (is_path_in_env(current_path, install_dir.string())) {
                output::warn("The install directory '{}' is in your PATH environment variable.", install_dir.string());
                output::warn("This can lead to unexpected behavior. Consider removing it from PATH.");
                output::warn("Scoop will add the necessary directories to PATH automatically.");
            }
        } catch (const std::exception& e) {
            output::debug("Failed to check PATH for install directory: {}", e.what());
        }
    }

    void create_startmenu_shortcuts(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's create_startmenu_shortcuts function
        auto shortcuts = manifest.get_shortcuts(options_.architecture);
        if (shortcuts.empty()) return;

        // Get start menu folder
        auto start_menu_folder = get_start_menu_folder();
        if (start_menu_folder.empty()) {
            output::warn("Could not determine start menu folder");
            return;
        }

        for (const auto& shortcut : shortcuts) {
            if (shortcut.size() >= 2) {
                try {
                    std::string executable = shortcut[0];
                    std::string shortcut_name = shortcut[1];
                    std::string arguments = shortcut.size() >= 3 ? shortcut[2] : "";
                    std::string icon_path = shortcut.size() >= 4 ? shortcut[3] : "";

                    auto target_path = app_dir / executable;
                    if (!std::filesystem::exists(target_path)) {
                        output::error("Creating shortcut for {} ({}) failed: Couldn't find {}",
                                    shortcut_name, executable, target_path.string());
                        continue;
                    }

                    // Handle icon path
                    std::filesystem::path icon_full_path;
                    if (!icon_path.empty()) {
                        icon_full_path = app_dir / icon_path;
                        if (!std::filesystem::exists(icon_full_path)) {
                            output::error("Creating shortcut for {} ({}) failed: Couldn't find icon {}",
                                        shortcut_name, executable, icon_full_path.string());
                            icon_full_path.clear();
                        }
                    }

                    // Substitute variables in arguments
                    std::string processed_arguments = substitute_variables(arguments, app_dir, manifest.version);

                    // Handle subdirectory in shortcut name
                    auto shortcut_path = start_menu_folder / (shortcut_name + ".lnk");
                    if (shortcut_name.find('/') != std::string::npos || shortcut_name.find('\\') != std::string::npos) {
                        auto shortcut_dir = shortcut_path.parent_path();
                        std::filesystem::create_directories(shortcut_dir);
                    }

                    if (create_shortcut_file(target_path, shortcut_path, processed_arguments, icon_full_path)) {
                        std::cout << "Creating shortcut for " << shortcut_name << " (" << executable << ")\n";
                    } else {
                        output::error("Failed to create shortcut for {}", shortcut_name);
                    }

                } catch (const std::exception& e) {
                    output::error("Failed to create shortcut: {}", e.what());
                }
            }
        }
    }

    void install_psmodule(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's install_psmodule function
        auto psmodule = manifest.get_psmodule();
        if (psmodule.name.empty()) return;

        try {
            // Get PowerShell modules directory
            auto modules_dir = get_powershell_modules_dir();
            if (modules_dir.empty()) {
                output::warn("Could not determine PowerShell modules directory");
                return;
            }

            // Ensure modules directory exists
            std::filesystem::create_directories(modules_dir);

            // Ensure modules directory is in PSModulePath
            ensure_in_psmodule_path(modules_dir);

            auto module_link_path = modules_dir / psmodule.name;

            std::cout << "Installing PowerShell module '" << psmodule.name << "'\n";
            std::cout << "Linking " << module_link_path.string() << " => " << app_dir.string() << "\n";

            // Remove existing module link if it exists
            if (std::filesystem::exists(module_link_path)) {
                output::warn("{} already exists. It will be replaced.", module_link_path.string());
                std::filesystem::remove_all(module_link_path);
            }

            // Create junction/symlink to app directory
            create_module_link(module_link_path, app_dir);

        } catch (const std::exception& e) {
            output::error("Failed to install PowerShell module: {}", e.what());
        }
    }

    void env_add_path(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's env_add_path function
        auto env_add_path = manifest.get_env_add_path(options_.architecture);
        if (env_add_path.empty()) return;

        std::cout << "Adding to PATH environment variable...\n";

        for (const auto& [var_name, path_value] : env_add_path) {
            try {
                // Substitute variables in path value
                std::string expanded_path = substitute_variables(path_value, app_dir, manifest.version);

                // Convert relative path to absolute
                std::filesystem::path full_path = app_dir / expanded_path;
                if (std::filesystem::exists(full_path)) {
                    add_to_path_env(var_name, full_path.string());
                    output::info("Added {} to {} environment variable", full_path.string(), var_name);
                } else {
                    output::warn("Path does not exist, skipping: {}", full_path.string());
                }
            } catch (const std::exception& e) {
                output::error("Failed to add path to {}: {}", var_name, e.what());
            }
        }
    }

    void env_set(const Manifest& manifest) {
        // Scoop's env_set function
        auto env_set = manifest.get_env_set(options_.architecture);
        if (env_set.empty()) return;

        std::cout << "Setting environment variables...\n";

        for (const auto& [var_name, var_value] : env_set) {
            try {
                // Substitute variables in value
                std::string expanded_value = substitute_variables(var_value, std::filesystem::path(), manifest.version);

                set_env_var(var_name, expanded_value);
                output::info("Set environment variable {} = {}", var_name, expanded_value);
            } catch (const std::exception& e) {
                output::error("Failed to set environment variable {}: {}", var_name, e.what());
            }
        }
    }

    void persist_permission(const Manifest& manifest) {
        // Scoop's persist_permission function
        // This function would handle setting permissions on persist directories
        // For now, we'll implement basic permission handling

        auto persist_dirs = manifest.get_persist();
        if (persist_dirs.empty()) return;

        auto persist_base_dir = get_persist_directory(manifest.name);

        try {
            for (const auto& persist_entry : persist_dirs) {
                std::string target_path = persist_entry;

                // Handle array format if needed
                if (persist_entry.find(',') != std::string::npos) {
                    size_t comma_pos = persist_entry.find(',');
                    target_path = persist_entry.substr(comma_pos + 1);
                    target_path.erase(0, target_path.find_first_not_of(" \t"));
                    target_path.erase(target_path.find_last_not_of(" \t") + 1);
                }

                auto target_full_path = persist_base_dir / target_path;

                if (std::filesystem::exists(target_full_path)) {
                    // Set appropriate permissions for the persist directory
                    set_persist_permissions(target_full_path);
                }
            }
        } catch (const std::exception& e) {
            output::error("Failed to set persist permissions: {}", e.what());
        }
    }

    // Helper methods for installer execution
    std::string extract_filename_from_url(const std::string& url) {
        size_t last_slash = url.find_last_of('/');
        if (last_slash != std::string::npos) {
            std::string filename = url.substr(last_slash + 1);
            // Remove query parameters
            size_t query_pos = filename.find('?');
            if (query_pos != std::string::npos) {
                filename = filename.substr(0, query_pos);
            }
            return filename;
        }
        return "installer";
    }

    bool is_path_within_directory(const std::filesystem::path& directory, const std::filesystem::path& path) {
        try {
            auto rel_path = std::filesystem::relative(path, directory);
            return !rel_path.empty() && rel_path.string().substr(0, 2) != "..";
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string substitute_variables(const std::string& input, const std::filesystem::path& dir, const std::string& version) {
        std::string result = input;

        // Replace $dir with directory path
        std::regex dir_regex(R"(\$dir)");
        result = std::regex_replace(result, dir_regex, dir.string());

        // Replace $global with global flag
        std::regex global_regex(R"(\$global)");
        result = std::regex_replace(result, global_regex, options_.global ? "true" : "false");

        // Replace $version with version
        std::regex version_regex(R"(\$version)");
        result = std::regex_replace(result, version_regex, version);

        return result;
    }

    bool execute_powershell_script(const std::filesystem::path& script_path, const std::vector<std::string>& args) {
        try {
            std::ostringstream command;
            command << "powershell.exe -ExecutionPolicy Bypass -File \"" << script_path.string() << "\"";

            for (const auto& arg : args) {
                command << " \"" << arg << "\"";
            }

            output::debug("Executing PowerShell script: {}", command.str());

            int exit_code = system(command.str().c_str());
            return exit_code == 0;

        } catch (const std::exception& e) {
            output::error("Failed to execute PowerShell script: {}", e.what());
            return false;
        }
    }

    bool execute_installer(const std::filesystem::path& installer_path, const std::vector<std::string>& args, const std::string& type) {
#ifdef _WIN32
        try {
            std::ostringstream command;
            command << "\"" << installer_path.string() << "\"";

            for (const auto& arg : args) {
                command << " " << arg;
            }

            output::debug("Executing {}: {}", type, command.str());

            // Use ShellExecute for better compatibility with installers that require elevation
            SHELLEXECUTEINFOA sei = {};
            sei.cbSize = sizeof(sei);
            sei.fMask = SEE_MASK_NOCLOSEPROCESS | SEE_MASK_NOASYNC;
            sei.lpVerb = "open";
            sei.lpFile = installer_path.string().c_str();

            std::string args_str;
            for (size_t i = 0; i < args.size(); ++i) {
                if (i > 0) args_str += " ";
                args_str += args[i];
            }
            sei.lpParameters = args_str.empty() ? nullptr : args_str.c_str();
            sei.nShow = SW_HIDE;

            if (!ShellExecuteExA(&sei)) {
                output::error("Failed to execute {}", type);
                return false;
            }

            if (sei.hProcess) {
                // Wait for the installer to complete
                DWORD wait_result = WaitForSingleObject(sei.hProcess, INFINITE);
                DWORD exit_code = 0;
                GetExitCodeProcess(sei.hProcess, &exit_code);
                CloseHandle(sei.hProcess);

                return wait_result == WAIT_OBJECT_0 && exit_code == 0;
            }

            return true;

        } catch (const std::exception& e) {
            output::error("Failed to execute {}: {}", type, e.what());
            return false;
        }
#else
        // On non-Windows systems, use system() as fallback
        try {
            std::ostringstream command;
            command << "\"" << installer_path.string() << "\"";

            for (const auto& arg : args) {
                command << " \"" << arg << "\"";
            }

            output::debug("Executing {}: {}", type, command.str());

            int exit_code = system(command.str().c_str());
            return exit_code == 0;

        } catch (const std::exception& e) {
            output::error("Failed to execute {}: {}", type, e.what());
            return false;
        }
#endif
    }

    // Environment variable management methods
    void set_env_var(const std::string& name, const std::string& value) {
#ifdef _WIN32
        try {
            HKEY key = options_.global ? HKEY_LOCAL_MACHINE : HKEY_CURRENT_USER;
            std::string subkey = options_.global ?
                "SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment" :
                "Environment";

            HKEY env_key;
            LONG result = RegOpenKeyExA(key, subkey.c_str(), 0, KEY_SET_VALUE, &env_key);
            if (result != ERROR_SUCCESS) {
                output::error("Failed to open registry key for environment variables");
                return;
            }

            if (value.empty()) {
                // Delete the environment variable
                RegDeleteValueA(env_key, name.c_str());
            } else {
                // Set the environment variable
                DWORD value_type = value.find('%') != std::string::npos ? REG_EXPAND_SZ : REG_SZ;
                result = RegSetValueExA(env_key, name.c_str(), 0, value_type,
                                      reinterpret_cast<const BYTE*>(value.c_str()),
                                      static_cast<DWORD>(value.length() + 1));
                if (result != ERROR_SUCCESS) {
                    output::error("Failed to set environment variable {}", name);
                }
            }

            RegCloseKey(env_key);

            // Notify system of environment change
            SendMessageTimeoutA(HWND_BROADCAST, WM_SETTINGCHANGE, 0,
                               reinterpret_cast<LPARAM>("Environment"),
                               SMTO_ABORTIFHUNG, 5000, nullptr);

            // Also set in current process environment
            if (value.empty()) {
                _putenv_s(name.c_str(), "");
            } else {
                _putenv_s(name.c_str(), value.c_str());
            }

        } catch (const std::exception& e) {
            output::error("Failed to set environment variable {}: {}", name, e.what());
        }
#else
        // On non-Windows systems, just set in current process
        if (value.empty()) {
            unsetenv(name.c_str());
        } else {
            setenv(name.c_str(), value.c_str(), 1);
        }
#endif
    }

    void add_to_path_env(const std::string& var_name, const std::string& path_to_add) {
#ifdef _WIN32
        try {
            // Get current PATH value
            std::string current_path = get_env_var(var_name);

            // Check if path is already in PATH
            if (is_path_in_env(current_path, path_to_add)) {
                output::debug("Path {} already in {} environment variable", path_to_add, var_name);
                return;
            }

            // Add path to the beginning of PATH
            std::string new_path = path_to_add;
            if (!current_path.empty()) {
                new_path += ";" + current_path;
            }

            set_env_var(var_name, new_path);

        } catch (const std::exception& e) {
            output::error("Failed to add path to {}: {}", var_name, e.what());
        }
#else
        // On non-Windows systems, just set in current process
        std::string current_path = getenv(var_name.c_str()) ? getenv(var_name.c_str()) : "";
        if (!is_path_in_env(current_path, path_to_add)) {
            std::string new_path = path_to_add;
            if (!current_path.empty()) {
                new_path += ":" + current_path;
            }
            setenv(var_name.c_str(), new_path.c_str(), 1);
        }
#endif
    }

    std::string get_env_var(const std::string& name) {
#ifdef _WIN32
        try {
            HKEY key = options_.global ? HKEY_LOCAL_MACHINE : HKEY_CURRENT_USER;
            std::string subkey = options_.global ?
                "SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment" :
                "Environment";

            HKEY env_key;
            LONG result = RegOpenKeyExA(key, subkey.c_str(), 0, KEY_QUERY_VALUE, &env_key);
            if (result != ERROR_SUCCESS) {
                return "";
            }

            DWORD value_size = 0;
            result = RegQueryValueExA(env_key, name.c_str(), nullptr, nullptr, nullptr, &value_size);
            if (result != ERROR_SUCCESS || value_size == 0) {
                RegCloseKey(env_key);
                return "";
            }

            std::string value(value_size - 1, '\0');
            result = RegQueryValueExA(env_key, name.c_str(), nullptr, nullptr,
                                    reinterpret_cast<BYTE*>(&value[0]), &value_size);

            RegCloseKey(env_key);

            if (result == ERROR_SUCCESS) {
                return value;
            }

        } catch (const std::exception&) {
            // Fall through to return empty string
        }
#else
        const char* value = getenv(name.c_str());
        if (value) {
            return std::string(value);
        }
#endif
        return "";
    }

    bool is_path_in_env(const std::string& env_value, const std::string& path_to_check) {
        if (env_value.empty() || path_to_check.empty()) {
            return false;
        }

#ifdef _WIN32
        char delimiter = ';';
#else
        char delimiter = ':';
#endif

        std::istringstream iss(env_value);
        std::string path;

        while (std::getline(iss, path, delimiter)) {
            try {
                // Normalize paths for comparison
                std::filesystem::path existing_path = std::filesystem::canonical(path);
                std::filesystem::path check_path = std::filesystem::canonical(path_to_check);

                if (existing_path == check_path) {
                    return true;
                }
            } catch (const std::exception&) {
                // If canonical fails, do string comparison
                if (path == path_to_check) {
                    return true;
                }
            }
        }

        return false;
    }

    // Persist directory management methods
    std::filesystem::path get_persist_directory(const std::string& app_name) {
        auto persist_dir = options_.global ? config_.get_global_apps_dir().parent_path() / "persist"
                                          : config_.get_apps_dir().parent_path() / "persist";
        return persist_dir / app_name;
    }

    void copy_directory_contents(const std::filesystem::path& source, const std::filesystem::path& target) {
        try {
            for (const auto& entry : std::filesystem::recursive_directory_iterator(source)) {
                auto relative_path = std::filesystem::relative(entry.path(), source);
                auto target_path = target / relative_path;

                if (entry.is_directory()) {
                    std::filesystem::create_directories(target_path);
                } else {
                    std::filesystem::create_directories(target_path.parent_path());
                    std::filesystem::copy_file(entry.path(), target_path);
                }
            }
        } catch (const std::exception& e) {
            output::error("Failed to copy directory contents: {}", e.what());
        }
    }

    void create_persist_link(const std::filesystem::path& source, const std::filesystem::path& target) {
        try {
            // Ensure target exists
            if (!std::filesystem::exists(target)) {
                if (source.filename().extension().empty()) {
                    // Assume it's a directory
                    std::filesystem::create_directories(target);
                } else {
                    // Assume it's a file
                    std::filesystem::create_directories(target.parent_path());
                    std::ofstream file(target);
                    file.close();
                }
            }

#ifdef _WIN32
            // On Windows, create a junction point for directories or hard link for files
            if (std::filesystem::is_directory(target)) {
                std::string command = "mklink /J \"" + source.string() + "\" \"" + target.string() + "\"";
                int result = system(command.c_str());
                if (result != 0) {
                    output::warn("Failed to create junction for {}, using directory copy instead", source.string());
                    std::filesystem::create_directories(source);
                    copy_directory_contents(target, source);
                }
            } else {
                // For files, create hard link
                std::string command = "mklink /H \"" + source.string() + "\" \"" + target.string() + "\"";
                int result = system(command.c_str());
                if (result != 0) {
                    output::warn("Failed to create hard link for {}, using file copy instead", source.string());
                    std::filesystem::copy_file(target, source);
                }
            }
#else
            // On Unix-like systems, create symbolic links
            std::filesystem::create_symlink(target, source);
#endif

        } catch (const std::exception& e) {
            output::error("Failed to create persist link from {} to {}: {}", source.string(), target.string(), e.what());
        }
    }

    // Shortcut creation methods
    std::filesystem::path get_start_menu_folder() {
#ifdef _WIN32
        try {
            // Get the start menu programs folder
            char path[MAX_PATH];
            if (SUCCEEDED(SHGetFolderPathA(nullptr,
                                         options_.global ? CSIDL_COMMON_PROGRAMS : CSIDL_PROGRAMS,
                                         nullptr, SHGFP_TYPE_CURRENT, path))) {
                auto start_menu_path = std::filesystem::path(path) / "Scoop Apps";
                std::filesystem::create_directories(start_menu_path);
                return start_menu_path;
            }
        } catch (const std::exception& e) {
            output::error("Failed to get start menu folder: {}", e.what());
        }
#endif
        return {};
    }

    bool create_shortcut_file(const std::filesystem::path& target_path,
                            const std::filesystem::path& shortcut_path,
                            const std::string& arguments,
                            const std::filesystem::path& icon_path) {
#ifdef _WIN32
        try {
            // Initialize COM
            HRESULT hr = CoInitialize(nullptr);
            if (FAILED(hr)) {
                output::error("Failed to initialize COM");
                return false;
            }

            // Create IShellLink object
            IShellLinkA* shell_link = nullptr;
            hr = CoCreateInstance(CLSID_ShellLink, nullptr, CLSCTX_INPROC_SERVER,
                                IID_IShellLinkA, reinterpret_cast<void**>(&shell_link));
            if (FAILED(hr)) {
                output::error("Failed to create IShellLink object");
                CoUninitialize();
                return false;
            }

            // Set target path
            shell_link->SetPath(target_path.string().c_str());

            // Set working directory
            shell_link->SetWorkingDirectory(target_path.parent_path().string().c_str());

            // Set arguments if provided
            if (!arguments.empty()) {
                shell_link->SetArguments(arguments.c_str());
            }

            // Set icon if provided
            if (!icon_path.empty() && std::filesystem::exists(icon_path)) {
                shell_link->SetIconLocation(icon_path.string().c_str(), 0);
            }

            // Get IPersistFile interface
            IPersistFile* persist_file = nullptr;
            hr = shell_link->QueryInterface(IID_IPersistFile, reinterpret_cast<void**>(&persist_file));
            if (FAILED(hr)) {
                output::error("Failed to get IPersistFile interface");
                shell_link->Release();
                CoUninitialize();
                return false;
            }

            // Convert path to wide string
            std::wstring wide_path = std::filesystem::path(shortcut_path).wstring();

            // Save the shortcut
            hr = persist_file->Save(wide_path.c_str(), TRUE);

            // Clean up
            persist_file->Release();
            shell_link->Release();
            CoUninitialize();

            return SUCCEEDED(hr);

        } catch (const std::exception& e) {
            output::error("Failed to create shortcut: {}", e.what());
            CoUninitialize();
            return false;
        }
#else
        // On non-Windows systems, create a simple script file
        try {
            std::ofstream script(shortcut_path);
            script << "#!/bin/bash\n";
            script << "cd \"" << target_path.parent_path().string() << "\"\n";
            script << "\"" << target_path.string() << "\"";
            if (!arguments.empty()) {
                script << " " << arguments;
            }
            script << "\n";
            script.close();

            // Make script executable
            std::filesystem::permissions(shortcut_path,
                                       std::filesystem::perms::owner_exec |
                                       std::filesystem::perms::group_exec |
                                       std::filesystem::perms::others_exec,
                                       std::filesystem::perm_options::add);
            return true;

        } catch (const std::exception& e) {
            output::error("Failed to create shortcut script: {}", e.what());
            return false;
        }
#endif
    }

    // PowerShell module management methods
    std::filesystem::path get_powershell_modules_dir() {
        try {
            // Get user's PowerShell modules directory
            auto modules_dir = options_.global ?
                get_global_powershell_modules_dir() :
                get_user_powershell_modules_dir();

            return modules_dir;
        } catch (const std::exception& e) {
            output::error("Failed to get PowerShell modules directory: {}", e.what());
            return {};
        }
    }

    std::filesystem::path get_user_powershell_modules_dir() {
#ifdef _WIN32
        // Get user's Documents folder
        char path[MAX_PATH];
        if (SUCCEEDED(SHGetFolderPathA(nullptr, CSIDL_MYDOCUMENTS, nullptr, SHGFP_TYPE_CURRENT, path))) {
            return std::filesystem::path(path) / "PowerShell" / "Modules";
        }
#else
        // On Unix-like systems, use ~/.local/share/powershell/Modules
        const char* home = getenv("HOME");
        if (home) {
            return std::filesystem::path(home) / ".local" / "share" / "powershell" / "Modules";
        }
#endif
        return {};
    }

    std::filesystem::path get_global_powershell_modules_dir() {
#ifdef _WIN32
        // Use Program Files\PowerShell\Modules
        char path[MAX_PATH];
        if (SUCCEEDED(SHGetFolderPathA(nullptr, CSIDL_PROGRAM_FILES, nullptr, SHGFP_TYPE_CURRENT, path))) {
            return std::filesystem::path(path) / "PowerShell" / "Modules";
        }
#else
        // On Unix-like systems, use /usr/local/share/powershell/Modules
        return std::filesystem::path("/usr/local/share/powershell/Modules");
#endif
        return {};
    }

    void ensure_in_psmodule_path(const std::filesystem::path& modules_dir) {
        try {
            // Get current PSModulePath
            std::string current_path = get_env_var("PSModulePath");

            // Check if modules directory is already in PSModulePath
            if (is_path_in_env(current_path, modules_dir.string())) {
                return; // Already in path
            }

            // Add modules directory to PSModulePath
            std::string new_path = modules_dir.string();
            if (!current_path.empty()) {
#ifdef _WIN32
                new_path += ";" + current_path;
#else
                new_path += ":" + current_path;
#endif
            }

            set_env_var("PSModulePath", new_path);
            output::info("Added {} to PSModulePath", modules_dir.string());

        } catch (const std::exception& e) {
            output::error("Failed to update PSModulePath: {}", e.what());
        }
    }

    void create_module_link(const std::filesystem::path& link_path, const std::filesystem::path& target_path) {
        try {
#ifdef _WIN32
            // On Windows, create a junction point
            std::string command = "mklink /J \"" + link_path.string() + "\" \"" + target_path.string() + "\"";
            int result = system(command.c_str());
            if (result != 0) {
                output::warn("Failed to create junction for PowerShell module, using directory copy instead");
                std::filesystem::create_directories(link_path);
                copy_directory_contents(target_path, link_path);
            }
#else
            // On Unix-like systems, create a symbolic link
            std::filesystem::create_symlink(target_path, link_path);
#endif
        } catch (const std::exception& e) {
            output::error("Failed to create module link from {} to {}: {}",
                        link_path.string(), target_path.string(), e.what());
        }
    }

    // Permission management methods
    void set_persist_permissions(const std::filesystem::path& persist_path) {
        try {
#ifdef _WIN32
            // On Windows, ensure the current user has full control over the persist directory
            // This is a simplified implementation - in a full implementation, you might want
            // to use Windows ACL APIs for more precise control

            // For now, just ensure the directory is writable
            auto perms = std::filesystem::status(persist_path).permissions();
            if ((perms & std::filesystem::perms::owner_write) == std::filesystem::perms::none) {
                std::filesystem::permissions(persist_path,
                                           std::filesystem::perms::owner_all,
                                           std::filesystem::perm_options::add);
            }
#else
            // On Unix-like systems, set appropriate permissions
            std::filesystem::permissions(persist_path,
                                       std::filesystem::perms::owner_all |
                                       std::filesystem::perms::group_read |
                                       std::filesystem::perms::group_exec,
                                       std::filesystem::perm_options::replace);
#endif

            output::debug("Set permissions for persist directory: {}", persist_path.string());

        } catch (const std::exception& e) {
            output::warn("Failed to set permissions for persist directory {}: {}",
                        persist_path.string(), e.what());
        }
    }
};

} // namespace sco
